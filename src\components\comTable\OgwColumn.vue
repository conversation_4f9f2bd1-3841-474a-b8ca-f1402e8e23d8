<!--表单表格列 -->
<template>
  <el-table-column
    :label="column.label"
    :prop="column.prop"
    :width="column.width"
    :min-width="column.minWidth || 120"
    :fixed="column.fixed"
    :align="column.align || 'center'"
  >
    <!-- 如果有子列，递归渲染 -->
    <template v-if="column.children && column.children.length">
      <OgwColumn
        v-for="child in column.children"
        :key="child.prop"
        :column="child"
        :show-index="showIndex"
        :summary-config="summaryConfig"
        :fixed="column.fixed"
        @enter-edit="$emit('enter-edit', $event)"
        @exit-edit="$emit('exit-edit', $event)"
        @cell-change="$emit('cell-change', $event)"
        @cell-click="$emit('cell-click', $event)"
      >
        <!-- 透传插槽 -->
        <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
          <slot :name="slotName" v-bind="scope" />
        </template>
      </OgwColumn>
    </template>

    <!-- 没有子列，渲染单元格内容 -->
    <template v-if="!column.children" v-slot="scope">
      <template v-if="scope.row._isSummaryRow">
        <template
          v-if="isFirstSummaryTextColumn(scope.row._summaryType, column.prop)"
        >
          <span class="summary-text">{{ scope.row._summaryText }}</span>
        </template>
        <template v-else-if="summaryConfig.sumColumns.includes(column.prop)">
          <span class="summary-value">{{ scope.row[column.prop] }}</span>
        </template>
      </template>

      <template v-else>
        <!-- 编辑模式 -->
        <template
          v-if="column.editable && isEditingCell(scope.$index, column.prop)"
        >
          <div class="editing-input" @click.stop>
            <!-- 下拉选择器 -->
            <el-select
              v-if="column.options"
              v-model="scope.row[column.prop]"
              size="small"
              style="width: 100%"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleEditBlur"
            >
              <el-option
                v-for="opt in column.options"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="column.editType === 'date'"
              ref="datePicker"
              v-model="scope.row[column.prop]"
              :type="getDatePickerType(column)"
              :format="getDateFormat(column)"
              :value-format="getDateValueFormat(column)"
              :placeholder="getDatePlaceholder(column)"
              size="small"
              style="width: 100%"
              :clearable="false"
              :append-to-body="true"
              :popper-class="'table-date-picker'"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleDatePickerBlur"
              @focus="handleDatePickerFocus"
            />
            <!-- 输入框编辑组件 -->
            <div v-else class="input-wrapper">
              <component
                :is="getEditComponent(column)"
                v-model="scope.row[column.prop]"
                size="small"
                :class="getInputClass(column, scope.row[column.prop])"
                @blur="handleInputBlur(scope.row, column.prop, scope.$index)"
                @keyup.enter="handleEditEnter"
                @input="handleInputChange(scope.row, column.prop, scope.$index, $event)"
                @keypress="handleKeyPress(column, $event)"
                style="width: 100%"
              />
              <!-- 验证错误提示 -->
              <div
                v-if="getValidationError(column, scope.row[column.prop])"
                class="validation-error"
              >
                {{ getValidationError(column, scope.row[column.prop]) }}
              </div>
            </div>
          </div>
        </template>

        <!-- 可编辑但未在编辑状态 -->
        <template v-else-if="column.editable">
          <div
            :class="[
              'editable-cell',
              { 'edit-success': isEditSuccess(scope.$index, column.prop) }
            ]"
            @click="handleEditableOrClickableClick($event, scope.$index, column.prop, scope.row)"
            @mouseenter="handleCellHover(true)"
            @mouseleave="handleCellHover(false)"
          >
            <el-tooltip
              v-if="shouldShowTooltip(column, scope.row)"
              :content="getTooltipContent(column, scope.row)"
              :disabled="!shouldShowTooltip(column, scope.row)"
              placement="top"
              :open-delay="500"
              :popper-class="getTooltipClass()"
            >
              <span class="cell-content">
                {{ getFormattedValue(column, scope.row) || "-" }}
              </span>
            </el-tooltip>
            <span v-else class="cell-content">
              {{ getFormattedValue(column, scope.row) || "-" }}
            </span>
            <i class="el-icon-edit edit-icon"></i>
          </div>
        </template>

        <!-- 可点击但不可编辑 -->
        <template v-else-if="column.clickable">
          <el-tooltip
            v-if="shouldShowTooltip(column, scope.row)"
            :content="getTooltipContent(column, scope.row)"
            :disabled="!shouldShowTooltip(column, scope.row)"
            placement="top"
            :open-delay="500"
            :popper-class="getTooltipClass()"
          >
            <span
              class="clickable-cell cell-with-tooltip"
              @click="handleClickableClick($event, scope.row, column.prop, scope.$index)"
            >
              <slot :name="column.prop" v-bind="scope">
                {{ getFormattedValue(column, scope.row) }}
              </slot>
            </span>
          </el-tooltip>
          <span
            v-else
            class="clickable-cell"
            @click="handleClickableClick($event, scope.row, column.prop, scope.$index)"
          >
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>

        <!-- 普通单元格 -->
        <template v-else>
          <el-tooltip
            v-if="shouldShowTooltip(column, scope.row)"
            :content="getTooltipContent(column, scope.row)"
            :disabled="!shouldShowTooltip(column, scope.row)"
            placement="top"
            :open-delay="500"
            :popper-class="getTooltipClass()"
          >
            <span class="normal-cell cell-with-tooltip">
              <slot :name="column.prop" v-bind="scope">
                {{ getFormattedValue(column, scope.row) }}
              </slot>
            </span>
          </el-tooltip>
          <span v-else class="normal-cell">
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>
      </template>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: "OgwColumn",
  props: {
    column: Object,
    showIndex: Boolean,
    summaryConfig: Object,
    editingCell: {
      type: Object,
      default: () => ({ rowIndex: null, prop: null }),
    },
  },
  data() {
    return {
      originalValue: null, // 存储编辑前的原始值
      currentEditingKey: null, // 当前编辑的单元格标识
      isHovering: false, // 鼠标悬停状态
      editSuccessKey: null, // 编辑成功的单元格标识
    };
  },
  watch: {
    editingCell: {
      handler(newVal, oldVal) {
        // 监听编辑状态变化
        const newKey = newVal.rowIndex !== null ? `${newVal.rowIndex}-${newVal.prop}` : null;
        const oldKey = oldVal && oldVal.rowIndex !== null ? `${oldVal.rowIndex}-${oldVal.prop}` : null;

        if (oldKey && oldKey !== newKey && this.currentEditingKey === oldKey) {
          // 编辑状态结束，且是当前组件负责的编辑
          // 如果有原始值，说明可能是取消编辑，需要回滚
          if (this.originalValue !== null) {
            // 这里可以选择是否回滚，暂时不自动回滚，让用户决定
            this.originalValue = null;
          }
          this.currentEditingKey = null;
        }

        if (newKey && newKey !== oldKey) {
          // 新的编辑开始
          const currentKey = `${newVal.rowIndex}-${this.column.prop}`;
          if (newKey === currentKey) {
            this.currentEditingKey = newKey;
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    isFirstSummaryTextColumn(type, prop) {
      const mergeCols =
        type === "grand"
          ? this.summaryConfig.grandTotalTextMergeColumns
          : type === "sub"
          ? this.summaryConfig.subTotalTextMergeColumns
          : type === "customSub"
          ? this.summaryConfig.customSubTotalTextMergeColumns
          : type === "customGrand"
          ? this.summaryConfig.customGrandTotalTextMergeColumns
          : [];
      return mergeCols.length && mergeCols[0] === prop;
    },
    isEditingCell(index, prop) {
      return (
        this.editingCell.rowIndex === index && this.editingCell.prop === prop
      );
    },
    getFormattedValue(col, row) {
      const value = row[col.prop];
      if (col.formatter) {
        return col.formatter(row, value);
      }
      if (col.options) {
        const match = col.options.find((opt) => opt.value === value);
        return match ? match.label : value;
      }
      // 日期类型的格式化显示
      if (col.editType === 'date' && value) {
        return this.formatDateValue(value, col);
      }
      return value;
    },
    formatDateValue(value, column) {
      if (!value) return '';

      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) return value;

        const type = this.getDatePickerType(column);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return type === 'month' ? `${year}年${month}月` : `${year}-${month}-${day}`;
      } catch (error) {
        console.warn('日期格式化失败:', error);
        return value;
      }
    },
    getEditComponent(column) {
      // 根据列配置返回对应的编辑组件
      if (column.editComponent) {
        return column.editComponent;
      }
      // 默认使用 el-input
      return 'el-input';
    },
    // 日期选择器相关方法
    getDatePickerType(column) {
      return column.dateConfig?.type || 'date';
    },
    getDateFormat(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.format) {
        return column.dateConfig.format;
      }
      // 默认格式
      return type === 'month' ? 'yyyy年MM月' : 'yyyy-MM-dd';
    },
    getDateValueFormat(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.valueFormat) {
        return column.dateConfig.valueFormat;
      }
      // 默认值格式
      return type === 'month' ? 'yyyy-MM' : 'yyyy-MM-dd';
    },
    getDatePlaceholder(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.placeholder) {
        return column.dateConfig.placeholder;
      }
      // 默认占位符
      return type === 'month' ? '请选择月份' : '请选择日期';
    },
    handleEditableClick(event, index, prop, row) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 保存原始值，用于验证失败时回滚
      this.originalValue = row[prop];
      // 进入编辑模式
      this.$emit('enter-edit', { index, prop });
    },
    handleClickableClick(event, row, prop, index) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 发出点击事件
      this.$emit('cell-click', {
        event,
        row,
        prop,
        index,
      });
    },
    // 新增：处理既可编辑又可点击的单元格
    handleEditableOrClickableClick(event, index, prop, row) {
      // 阻止事件冒泡
      event.stopPropagation();

      // 如果列同时支持clickable，优先触发cell-click事件
      if (this.column.clickable) {
        this.$emit('cell-click', {
          event,
          row,
          prop,
          index,
        });
      } else {
        // 否则进入编辑模式
        this.originalValue = row[prop];
        this.$emit('enter-edit', { index, prop });
      }
    },
    handleEditChange(row, prop, index, value) {
      // 获取列配置
      const column = this.column;

      // 如果是日期类型，进行验证
      if (column.editType === 'date' && value) {
        const validatedValue = this.validateDateValue(value, column);
        if (validatedValue !== value) {
          // 如果验证后的值不同，更新行数据
          row[prop] = validatedValue;
          value = validatedValue;
        }
      }

      // 发出值变化事件
      this.$emit('cell-change', {
        row,
        prop,
        index,
        value,
      });

      // 显示编辑成功反馈
      this.showEditSuccess(index, prop);
    },
    validateDateValue(value, column) {
      if (!value) return value;

      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          console.warn('无效的日期值:', value);
          return '';
        }

        // 检查日期范围限制（如果配置了的话）
        if (column.dateConfig?.minDate) {
          const minDate = new Date(column.dateConfig.minDate);
          if (date < minDate) {
            console.warn('日期不能早于最小日期:', column.dateConfig.minDate);
            return column.dateConfig.minDate;
          }
        }

        if (column.dateConfig?.maxDate) {
          const maxDate = new Date(column.dateConfig.maxDate);
          if (date > maxDate) {
            console.warn('日期不能晚于最大日期:', column.dateConfig.maxDate);
            return column.dateConfig.maxDate;
          }
        }

        return value;
      } catch (error) {
        console.warn('日期验证失败:', error);
        return '';
      }
    },
    handleEditBlur() {
      // 延迟退出编辑，避免与点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 150);
    },
    handleEditEnter() {
      // 按回车键退出编辑，需要先进行验证
      // 获取当前编辑的单元格信息
      const editingCell = this.$parent.editingCell || this.$parent.$parent.editingCell;
      if (!editingCell || editingCell.rowIndex === null) {
        this.$emit('exit-edit');
        return;
      }

      // 获取当前行数据
      const tableData = this.$parent.tableWithSummaries || this.$parent.$parent.tableWithSummaries;
      if (!tableData || !tableData[editingCell.rowIndex]) {
        this.$emit('exit-edit');
        return;
      }

      const row = tableData[editingCell.rowIndex];
      const prop = editingCell.prop;
      const value = row[prop];

      // 如果有验证规则，进行验证
      if (this.column.validation) {
        const validation = this.validateInput(this.column, value);
        if (validation.valid) {
          // 验证通过，发出变化事件并退出编辑
          this.$emit('cell-change', {
            row,
            prop,
            index: editingCell.rowIndex,
            value,
          });
          // 清理原始值
          this.originalValue = null;
          this.$emit('exit-edit');
        } else {
          // 验证失败，回滚到原始值，不退出编辑模式
          console.warn('输入验证失败:', validation.message);
          // 回滚数据到原始值
          if (this.originalValue !== null) {
            row[prop] = this.originalValue;
          }
          // 强制更新视图
          this.$forceUpdate();
          // 不退出编辑模式
          return;
        }
      } else {
        // 没有验证规则，直接退出编辑
        // 清理原始值
        this.originalValue = null;
        this.$emit('exit-edit');
      }
    },
    handleDatePickerFocus() {
      // 日期选择器获得焦点时的处理
      this.$nextTick(() => {
        // 确保弹出层正确显示
        const datePicker = this.$refs.datePicker;
        if (datePicker && datePicker.focus) {
          datePicker.focus();
        }
      });
    },
    handleDatePickerBlur() {
      // 日期选择器失去焦点时的处理
      // 延迟退出编辑，避免与日期选择器弹出层点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 200);
    },
    // 输入验证相关方法
    validateInput(column, value) {
      if (!column.validation) return { valid: true, message: '' };

      const validation = column.validation;
      const stringValue = String(value || '');

      // 必填验证
      if (validation.required && !stringValue.trim()) {
        return {
          valid: false,
          message: validation.errorMessage || '此字段为必填项'
        };
      }

      // 如果值为空且非必填，则通过验证
      if (!stringValue.trim() && !validation.required) {
        return { valid: true, message: '' };
      }

      // 根据类型进行验证
      switch (validation.type) {
        case 'number':
          return this.validateNumber(stringValue, validation);
        case 'decimal':
          return this.validateDecimal(stringValue, validation);
        case 'regex':
          return this.validateRegex(stringValue, validation);
        case 'text':
        default:
          return this.validateText(stringValue, validation);
      }
    },
    validateNumber(value, validation) {
      const numberRegex = /^-?\d+$/;
      if (!numberRegex.test(value)) {
        return {
          valid: false,
          message: validation.errorMessage || '请输入有效的整数'
        };
      }

      const num = parseInt(value);
      if (validation.min !== undefined && num < validation.min) {
        return {
          valid: false,
          message: validation.errorMessage || `数值不能小于 ${validation.min}`
        };
      }

      if (validation.max !== undefined && num > validation.max) {
        return {
          valid: false,
          message: validation.errorMessage || `数值不能大于 ${validation.max}`
        };
      }

      return { valid: true, message: '' };
    },
    validateDecimal(value, validation) {
      const decimalRegex = /^-?\d*\.?\d*$/;
      if (!decimalRegex.test(value)) {
        return {
          valid: false,
          message: validation.errorMessage || '请输入有效的数字'
        };
      }

      // 检查小数位数
      if (validation.precision !== undefined) {
        const decimalPart = value.split('.')[1];
        if (decimalPart && decimalPart.length > validation.precision) {
          return {
            valid: false,
            message: validation.errorMessage || `小数位数不能超过 ${validation.precision} 位`
          };
        }
      }

      const num = parseFloat(value);
      if (!isNaN(num)) {
        if (validation.min !== undefined && num < validation.min) {
          return {
            valid: false,
            message: validation.errorMessage || `数值不能小于 ${validation.min}`
          };
        }

        if (validation.max !== undefined && num > validation.max) {
          return {
            valid: false,
            message: validation.errorMessage || `数值不能大于 ${validation.max}`
          };
        }
      }

      return { valid: true, message: '' };
    },
    validateRegex(value, validation) {
      if (!validation.pattern) {
        return { valid: true, message: '' };
      }

      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        return {
          valid: false,
          message: validation.errorMessage || '输入格式不正确'
        };
      }

      return { valid: true, message: '' };
    },
    validateText(value, validation) {
      // 文本长度验证
      if (validation.minLength !== undefined && value.length < validation.minLength) {
        return {
          valid: false,
          message: validation.errorMessage || `最少输入 ${validation.minLength} 个字符`
        };
      }

      if (validation.maxLength !== undefined && value.length > validation.maxLength) {
        return {
          valid: false,
          message: validation.errorMessage || `最多输入 ${validation.maxLength} 个字符`
        };
      }

      return { valid: true, message: '' };
    },
    // 输入事件处理方法
    handleInputChange(/* row, prop, index, value */) {
      // 实时验证（仅用于视觉反馈，不阻止输入）
      const column = this.column;
      if (column.validation) {
        // 触发重新渲染以更新样式
        this.$forceUpdate();
      }
    },
    handleInputBlur(row, prop, index) {
      // 失去焦点时进行完整验证
      const column = this.column;
      const value = row[prop];

      if (column.validation) {
        const validation = this.validateInput(column, value);
        if (validation.valid) {
          // 验证通过，发出变化事件并退出编辑
          this.$emit('cell-change', {
            row,
            prop,
            index,
            value,
          });
          // 清理原始值
          this.originalValue = null;
          this.handleEditBlur();
        } else {
          // 验证失败，回滚到原始值，不退出编辑模式
          console.warn('输入验证失败:', validation.message);
          // 回滚数据到原始值
          if (this.originalValue !== null) {
            row[prop] = this.originalValue;
          }
          // 强制更新视图
          this.$forceUpdate();
          // 不退出编辑模式，让用户继续编辑
          return;
        }
      } else {
        // 没有验证规则，直接处理
        this.$emit('cell-change', {
          row,
          prop,
          index,
          value,
        });
        // 清理原始值
        this.originalValue = null;
        this.handleEditBlur();
      }
    },
    handleKeyPress(column, event) {
      // 实时输入限制
      if (!column.validation) return;

      const validation = column.validation;
      const char = String.fromCharCode(event.charCode);

      switch (validation.type) {
        case 'number':
          // 只允许数字和负号
          if (!/[\d-]/.test(char)) {
            event.preventDefault();
          }
          break;
        case 'decimal':
          // 允许数字、小数点和负号
          if (!/[\d.-]/.test(char)) {
            event.preventDefault();
          }
          break;
        // text 和 regex 类型不限制实时输入
      }
    },
    getInputClass(column, value) {
      if (!column.validation) return '';

      const validation = this.validateInput(column, value);
      return validation.valid ? 'input-valid' : 'input-invalid';
    },
    getValidationError(column, value) {
      if (!column.validation) return '';

      const validation = this.validateInput(column, value);
      return validation.valid ? '' : validation.message;
    },
    handleCellHover(isHovering) {
      this.isHovering = isHovering;
    },
    isEditSuccess(index, prop) {
      return this.editSuccessKey === `${index}-${prop}`;
    },
    showEditSuccess(index, prop) {
      this.editSuccessKey = `${index}-${prop}`;
      // 1.5秒后清除成功状态
      setTimeout(() => {
        if (this.editSuccessKey === `${index}-${prop}`) {
          this.editSuccessKey = null;
        }
      }, 1500);
    },
    // ========== 文本截断和 Tooltip 相关方法 ==========
    /**
     * 判断是否需要显示 tooltip
     * @param {Object} column - 列配置
     * @param {Object} row - 行数据
     * @returns {Boolean} 是否显示 tooltip
     */
    shouldShowTooltip(column, row) {
      // 如果列配置明确禁用 tooltip，则不显示
      if (column.showTooltip === false) {
        return false;
      }

      // 如果列配置明确启用 tooltip，则显示
      if (column.showTooltip === true) {
        return true;
      }

      // 获取格式化后的值
      const value = this.getFormattedValue(column, row);

      // 空值不显示 tooltip
      if (!value || value === '-') {
        return false;
      }

      // 转换为字符串进行长度判断
      const stringValue = String(value);

      // 根据列宽度和字符长度智能判断是否需要 tooltip
      const maxLength = this.getMaxTextLength(column);

      return stringValue.length > maxLength;
    },

    /**
     * 获取 tooltip 显示内容
     * @param {Object} column - 列配置
     * @param {Object} row - 行数据
     * @returns {String} tooltip 内容
     */
    getTooltipContent(column, row) {
      // 如果配置了自定义 tooltip 内容
      if (column.tooltipFormatter && typeof column.tooltipFormatter === 'function') {
        return column.tooltipFormatter(row, this.getFormattedValue(column, row));
      }

      // 默认显示格式化后的完整内容
      return this.getFormattedValue(column, row) || '';
    },

    /**
     * 获取 tooltip 的样式类
     * @returns {String} CSS 类名
     */
    getTooltipClass() {
      // 根据当前主题返回对应的 tooltip 样式类
      const theme = document.documentElement.getAttribute('data-theme') || 'default';
      return `ogw-table-tooltip ogw-table-tooltip-${theme}`;
    },

    /**
     * 根据列宽度计算最大文本长度
     * @param {Object} column - 列配置
     * @returns {Number} 最大字符长度
     */
    getMaxTextLength(column) {
      // 如果列配置了最大文本长度，使用配置值
      if (column.maxTextLength && typeof column.maxTextLength === 'number') {
        return column.maxTextLength;
      }

      // 根据列宽度智能计算最大字符长度
      const width = column.width || column.minWidth || 120;

      // 基于经验值：每个字符大约占用 14px（包括中英文混合的平均值）
      // 减去内边距和其他元素占用的空间（约 32px）
      const availableWidth = width - 32;
      const charWidth = 14;

      return Math.floor(availableWidth / charWidth);
    },
  },
};
</script>

<style lang="scss" scoped>
.summary-cell {
  font-weight: bold;
  color: #606266;
}

.clickable-cell {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}

// 浅色主题可编辑单元格样式
[data-theme="default"],
[data-theme="tint"] {
  .editable-cell {
    position: relative;
    cursor: pointer;
    color: #409eff;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 4px 8px;
    margin: -4px -8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 24px;

    .cell-content {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .edit-icon {
      opacity: 0;
      margin-left: 8px;
      font-size: 12px;
      color: #909399;
      transition: opacity 0.2s ease;
      flex-shrink: 0;
    }

    &:hover {
      background-color: #ecf5ff;
      border-color: #b3d8ff;

      .edit-icon {
        opacity: 1;
      }
    }

    &:active {
      background-color: #d9ecff;
      border-color: #409eff;
    }
  }
}

// 深色主题可编辑单元格样式
[data-theme="dark"] {
  .editable-cell {
    position: relative;
    cursor: pointer;
    color: #4EA0FC;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 4px 8px;
    margin: -4px -8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 24px;

    .cell-content {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .edit-icon {
      opacity: 0;
      margin-left: 8px;
      font-size: 12px;
      color: #CCE4FF;
      transition: opacity 0.2s ease;
      flex-shrink: 0;
    }

    &:hover {
      background-color: #254489;
      border-color: #4F98F6;

      .edit-icon {
        opacity: 1;
      }
    }

    &:active {
      background-color: #1A2E52;
      border-color: #4EA0FC;
    }
  }
}

// 浅色主题编辑状态样式
[data-theme="default"],
[data-theme="tint"] {
  ::v-deep .editing-input {
    position: relative;

    // 编辑状态指示器
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid #409eff;
      border-radius: 6px;
      pointer-events: none;
      z-index: 1;
      animation: editingPulse 2s infinite;
    }

    .el-input,
    .el-select,
    .el-date-editor {
      .el-input__inner {
        border-color: #409eff !important;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
        background-color: #fff !important;
        color: #000 !important;
      }
    }

    .el-date-editor {
      .el-input__prefix {
        color: #409eff;
      }
    }

    // 输入框验证样式
    .input-wrapper {
      position: relative;

      .input-valid {
        .el-input__inner {
          border-color: #67c23a;
          box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
        }
      }

      .input-invalid {
        .el-input__inner {
          border-color: #f56c6c;
          box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
        }
      }

      .validation-error {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #f56c6c;
        color: #fff;
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 0 0 4px 4px;
        z-index: 1000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 深色主题编辑状态样式
[data-theme="dark"] {
  ::v-deep .editing-input {
    position: relative;

    // 编辑状态指示器
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid #4EA0FC;
      border-radius: 6px;
      pointer-events: none;
      z-index: 1;
      animation: editingPulseDark 2s infinite;
    }

    .el-input,
    .el-select,
    .el-date-editor {
      .el-input__inner {
        border-color: #4F98F6 !important;
        box-shadow: 0 0 0 2px rgba(79, 152, 246, 0.2) !important;
        background-color: #1A2E52 !important;
        color: #ffffff !important;
      }
    }

    .el-date-editor {
      .el-input__prefix {
        color: #4EA0FC;
      }
    }

    // 输入框验证样式
    .input-wrapper {
      position: relative;

      .input-valid {
        .el-input__inner {
          border-color: #52C41A;
          box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
      }

      .input-invalid {
        .el-input__inner {
          border-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }
      }

      .validation-error {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #ff4d4f;
        color: #fff;
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 0 0 4px 4px;
        z-index: 1000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 浅色主题编辑状态动画
@keyframes editingPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

// 深色主题编辑状态动画
@keyframes editingPulseDark {
  0% {
    box-shadow: 0 0 0 0 rgba(78, 160, 252, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(78, 160, 252, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(78, 160, 252, 0);
  }
}

// 浅色主题编辑成功反馈动画
[data-theme="default"],
[data-theme="tint"] {
  @keyframes editSuccess {
    0% {
      background-color: #f0f9ff;
      border-color: #67c23a;
    }
    100% {
      background-color: transparent;
      border-color: transparent;
    }
  }

  .edit-success {
    animation: editSuccess 1s ease-out;
  }
}

// 深色主题编辑成功反馈动画
[data-theme="dark"] {
  @keyframes editSuccessDark {
    0% {
      background-color: #254489;
      border-color: #52C41A;
    }
    100% {
      background-color: transparent;
      border-color: transparent;
    }
  }

  .edit-success {
    animation: editSuccessDark 1s ease-out;
  }
}

// ========== 文本截断和 Tooltip 样式 ==========

// 通用单元格文本截断样式
.cell-content,
.normal-cell,
.clickable-cell {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

// 带 tooltip 的单元格样式
.cell-with-tooltip {
  cursor: help;

  &:hover {
    text-decoration: underline;
    text-decoration-style: dotted;
  }
}

// 浅色主题 tooltip 样式
[data-theme="default"],
[data-theme="tint"] {
  // 全局 tooltip 样式覆盖
  ::v-deep .ogw-table-tooltip-default,
  ::v-deep .ogw-table-tooltip-tint {
    .el-tooltip__popper {
      background-color: #303133 !important;
      color: #ffffff !important;
      border: 1px solid #303133 !important;
      border-radius: 6px !important;
      padding: 8px 12px !important;
      font-size: 12px !important;
      line-height: 1.4 !important;
      max-width: 300px !important;
      word-wrap: break-word !important;
      word-break: break-all !important;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
      z-index: 2000 !important;
    }

    .el-tooltip__popper[x-placement^="top"] .el-popper__arrow {
      border-top-color: #303133 !important;
    }

    .el-tooltip__popper[x-placement^="bottom"] .el-popper__arrow {
      border-bottom-color: #303133 !important;
    }

    .el-tooltip__popper[x-placement^="left"] .el-popper__arrow {
      border-left-color: #303133 !important;
    }

    .el-tooltip__popper[x-placement^="right"] .el-popper__arrow {
      border-right-color: #303133 !important;
    }
  }
}

// 深色主题 tooltip 样式
[data-theme="dark"] {
  ::v-deep .ogw-table-tooltip-dark {
    .el-tooltip__popper {
      background-color: #1a2e52 !important;
      color: #ffffff !important;
      border: 1px solid #4ea0fc !important;
      border-radius: 6px !important;
      padding: 8px 12px !important;
      font-size: 12px !important;
      line-height: 1.4 !important;
      max-width: 300px !important;
      word-wrap: break-word !important;
      word-break: break-all !important;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3) !important;
      z-index: 2000 !important;
    }

    .el-tooltip__popper[x-placement^="top"] .el-popper__arrow {
      border-top-color: #4ea0fc !important;
    }

    .el-tooltip__popper[x-placement^="bottom"] .el-popper__arrow {
      border-bottom-color: #4ea0fc !important;
    }

    .el-tooltip__popper[x-placement^="left"] .el-popper__arrow {
      border-left-color: #4ea0fc !important;
    }

    .el-tooltip__popper[x-placement^="right"] .el-popper__arrow {
      border-right-color: #4ea0fc !important;
    }
  }
}
</style>
